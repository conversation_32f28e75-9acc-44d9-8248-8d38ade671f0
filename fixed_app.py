#!/usr/bin/env python3
"""
Fixed Flask app specifically for image display issues
"""

from flask import Flask, render_template, request, jsonify, send_file, url_for, send_from_directory
import os
import uuid
import logging
import time
from werkzeug.utils import secure_filename
from face_slimmer import process_image, SUPPORTED_FORMATS

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'face-slimmer-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create necessary directories
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    if '.' not in filename:
        return False
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    return ext in SUPPORTED_FORMATS

@app.route('/')
def index():
    """Main page with upload form."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and processing."""
    input_path = None
    output_path = None
    
    try:
        logger.info("=== UPLOAD REQUEST RECEIVED ===")
        
        # Check if file is in request
        if 'file' not in request.files:
            logger.warning("No file in request")
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.warning("Empty filename")
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        logger.info(f"Processing file: {file.filename}")
        
        # Validate file format
        if not allowed_file(file.filename):
            supported = ', '.join(sorted(SUPPORTED_FORMATS))
            logger.warning(f"Unsupported file format: {file.filename}")
            return jsonify({'success': False, 'error': f'Unsupported file format. Supported formats: {supported}'}), 400
        
        # Generate unique filename with timestamp
        timestamp = str(int(time.time()))
        unique_id = str(uuid.uuid4())[:8]
        original_ext = '.' + file.filename.rsplit('.', 1)[1].lower()
        
        input_filename = f"{timestamp}_{unique_id}_input{original_ext}"
        output_filename = f"{timestamp}_{unique_id}_output.jpg"  # Always output as JPG
        
        input_path = os.path.join(UPLOAD_FOLDER, input_filename)
        output_path = os.path.join(PROCESSED_FOLDER, output_filename)
        
        # Save uploaded file
        logger.info(f"Saving file to: {os.path.abspath(input_path)}")
        file.save(input_path)
        
        # Verify file was saved
        if not os.path.exists(input_path) or os.path.getsize(input_path) == 0:
            logger.error(f"Failed to save input file properly: {input_path}")
            return jsonify({'success': False, 'error': 'Failed to save uploaded file'}), 500
        
        logger.info(f"Input file saved successfully, size: {os.path.getsize(input_path)} bytes")
        
        # Process the image
        logger.info("Starting image processing...")
        success = process_image(input_path, output_path)
        
        if not success or not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
            logger.error("Image processing failed or output file is invalid")
            # Clean up
            if os.path.exists(input_path):
                os.remove(input_path)
            if os.path.exists(output_path):
                os.remove(output_path)
            return jsonify({'success': False, 'error': 'Failed to process image. Please ensure the image contains a clear, visible face.'}), 400
        
        logger.info(f"Processing completed successfully! Output file size: {os.path.getsize(output_path)} bytes")
        
        # Return success response with simple file URLs
        response_data = {
            'success': True,
            'original_url': f'/uploads/{input_filename}',
            'processed_url': f'/processed/{output_filename}',
            'download_url': f'/download/{output_filename}',
            'original_filename': file.filename,
            'processed_filename': output_filename
        }
        
        logger.info(f"Returning success response: {response_data}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Unexpected error in upload_file: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Clean up files
        for path in [input_path, output_path]:
            if path and os.path.exists(path):
                try:
                    os.remove(path)
                except:
                    pass
        
        return jsonify({'success': False, 'error': f'An unexpected error occurred: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def serve_upload(filename):
    """Serve uploaded files."""
    try:
        filename = secure_filename(filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        
        logger.info(f"Serving upload file: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f"Upload file not found: {file_path}")
            return "File not found", 404
        
        # Send file with proper headers
        response = send_from_directory(UPLOAD_FOLDER, filename)
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        logger.info(f"Successfully served upload file: {filename}")
        return response
        
    except Exception as e:
        logger.error(f"Error serving upload file {filename}: {str(e)}")
        return f"Error serving file: {str(e)}", 500

@app.route('/processed/<filename>')
def serve_processed(filename):
    """Serve processed files."""
    try:
        filename = secure_filename(filename)
        file_path = os.path.join(PROCESSED_FOLDER, filename)
        
        logger.info(f"Serving processed file: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f"Processed file not found: {file_path}")
            return "File not found", 404
        
        # Send file with proper headers
        response = send_from_directory(PROCESSED_FOLDER, filename)
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        logger.info(f"Successfully served processed file: {filename}")
        return response
        
    except Exception as e:
        logger.error(f"Error serving processed file {filename}: {str(e)}")
        return f"Error serving file: {str(e)}", 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download processed file."""
    try:
        filename = secure_filename(filename)
        file_path = os.path.join(PROCESSED_FOLDER, filename)
        
        logger.info(f"Download requested for: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f"Download file not found: {file_path}")
            return "File not found", 404
        
        return send_from_directory(PROCESSED_FOLDER, filename, as_attachment=True, download_name=f"slimmed_{filename}")
        
    except Exception as e:
        logger.error(f"Error downloading file {filename}: {str(e)}")
        return f"Error downloading file: {str(e)}", 500

@app.route('/debug')
def debug_info():
    """Debug route to check server status."""
    try:
        uploads = []
        processed = []
        
        if os.path.exists(UPLOAD_FOLDER):
            uploads = os.listdir(UPLOAD_FOLDER)
        
        if os.path.exists(PROCESSED_FOLDER):
            processed = os.listdir(PROCESSED_FOLDER)
        
        return jsonify({
            'status': 'Flask server is running!',
            'timestamp': str(int(time.time())),
            'uploads_folder': os.path.abspath(UPLOAD_FOLDER),
            'processed_folder': os.path.abspath(PROCESSED_FOLDER),
            'uploads': uploads,
            'processed': processed,
            'supported_formats': list(SUPPORTED_FORMATS)
        })
    except Exception as e:
        logger.error(f"Debug route error: {str(e)}")
        return jsonify({'error': str(e), 'status': 'error'})

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    return jsonify({'success': False, 'error': 'File too large. Maximum size is 16MB.'}), 413

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return render_template('index.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle server errors."""
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 Starting Face Slimmer AI Web Application (FIXED VERSION)")
    print("=" * 60)
    print(f"📁 Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"📁 Processed folder: {os.path.abspath(PROCESSED_FOLDER)}")
    print(f"🎯 Supported formats: {', '.join(SUPPORTED_FORMATS)}")
    print("=" * 60)
    print("🌐 Server will be available at: http://127.0.0.1:5000")
    print("🔧 Debug info available at: http://127.0.0.1:5000/debug")
    print("=" * 60)
    
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
