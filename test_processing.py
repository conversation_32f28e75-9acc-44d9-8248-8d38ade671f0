#!/usr/bin/env python3
"""
Test script to verify face processing works correctly
"""

import os
import sys
from face_slimmer import process_image, SUPPORTED_FORMATS

def test_processing():
    print("Testing Face Slimmer Processing...")
    print(f"Supported formats: {SUPPORTED_FORMATS}")
    
    # Check if test image exists
    test_image = "face2.jpeg"
    if not os.path.exists(test_image):
        print(f"Test image {test_image} not found!")
        print("Available files:")
        for file in os.listdir('.'):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')):
                print(f"  - {file}")
        return False
    
    print(f"Processing test image: {test_image}")
    
    # Create output directories
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('processed', exist_ok=True)
    
    # Test processing
    output_path = "test_output.jpg"
    success = process_image(test_image, output_path)
    
    if success:
        print(f"✅ Processing successful! Output saved to: {output_path}")
        if os.path.exists(output_path):
            print(f"✅ Output file exists and is {os.path.getsize(output_path)} bytes")
        else:
            print("❌ Output file was not created")
            return False
    else:
        print("❌ Processing failed!")
        return False
    
    return True

if __name__ == "__main__":
    success = test_processing()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
