from flask import Flask, render_template, request, jsonify, send_file, url_for
import os
import uuid
import logging
from werkzeug.utils import secure_filename
from face_slimmer import process_image, validate_image_format, SUPPORTED_FORMATS
import tempfile
import shutil
from datetime import datetime
import traceback

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create necessary directories
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)
os.makedirs('static/css', exist_ok=True)
os.makedirs('static/js', exist_ok=True)
os.makedirs('templates', exist_ok=True)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    if '.' not in filename:
        return False
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    return ext in SUPPORTED_FORMATS

def cleanup_old_files():
    """Clean up old uploaded and processed files (older than 1 hour)."""
    import time
    current_time = time.time()
    
    for folder in [UPLOAD_FOLDER, PROCESSED_FOLDER]:
        for filename in os.listdir(folder):
            file_path = os.path.join(folder, filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getctime(file_path)
                if file_age > 3600:  # 1 hour
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass

@app.route('/')
def index():
    """Main page with upload form."""
    cleanup_old_files()
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and processing."""
    input_path = None
    try:
        logger.info("Received file upload request")
        
        if 'file' not in request.files:
            logger.warning("No file in request")
            return jsonify({'error': 'No file selected'}), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.warning("Empty filename")
            return jsonify({'error': 'No file selected'}), 400
        
        logger.info(f"Processing file: {file.filename}")
        
        if not allowed_file(file.filename):
            supported = ', '.join(sorted(SUPPORTED_FORMATS))
            logger.warning(f"Unsupported file format: {file.filename}")
            return jsonify({'error': f'Unsupported file format. Supported formats: {supported}'}), 400
        
        # Generate unique filename
        unique_id = str(uuid.uuid4())
        original_ext = '.' + file.filename.rsplit('.', 1)[1].lower()
        input_filename = f"{unique_id}_input{original_ext}"
        output_filename = f"{unique_id}_output{original_ext}"
        
        input_path = os.path.join(UPLOAD_FOLDER, input_filename)
        output_path = os.path.join(PROCESSED_FOLDER, output_filename)
        
        # Save uploaded file
        logger.info(f"Saving file to: {input_path}")
        file.save(input_path)
        
        # Process the image
        logger.info("Starting image processing")
        success = process_image(input_path, output_path)
        
        if not success:
            logger.error("Image processing failed")
            # Clean up input file
            if os.path.exists(input_path):
                os.remove(input_path)
            return jsonify({'error': 'Failed to process image. Please ensure the image contains a clear face.'}), 400
        
        logger.info("Image processing completed successfully")
        
        # Return success response with file URLs
        return jsonify({
            'success': True,
            'original_url': url_for('serve_file', folder='uploads', filename=input_filename),
            'processed_url': url_for('serve_file', folder='processed', filename=output_filename),
            'download_url': url_for('download_file', filename=output_filename)
        })
        
    except Exception as e:
        logger.error(f"Error in upload_file: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Clean up input file if it exists
        if input_path and os.path.exists(input_path):
            try:
                os.remove(input_path)
            except:
                pass
        
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@app.route('/files/<folder>/<filename>')
def serve_file(folder, filename):
    """Serve uploaded or processed files."""
    try:
        logger.info(f"Serving file: {folder}/{filename}")

        if folder not in ['uploads', 'processed']:
            logger.error(f"Invalid folder requested: {folder}")
            return "Invalid folder", 404

        file_path = os.path.join(folder, filename)
        logger.info(f"Looking for file at: {os.path.abspath(file_path)}")

        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return "File not found", 404

        logger.info(f"Successfully serving file: {file_path}")
        return send_file(file_path, mimetype='image/jpeg')

    except Exception as e:
        logger.error(f"Error serving file {folder}/{filename}: {str(e)}")
        return f"Error serving file: {str(e)}", 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download processed file."""
    try:
        file_path = os.path.join(PROCESSED_FOLDER, filename)
        logger.info(f"Download requested for: {file_path}")

        if not os.path.exists(file_path):
            logger.error(f"Download file not found: {file_path}")
            return "File not found", 404

        return send_file(file_path, as_attachment=True, download_name=f"slimmed_{filename}")

    except Exception as e:
        logger.error(f"Error downloading file {filename}: {str(e)}")
        return f"Error downloading file: {str(e)}", 500

@app.route('/debug/files')
def debug_files():
    """Debug route to check what files exist."""
    try:
        uploads = []
        processed = []

        if os.path.exists(UPLOAD_FOLDER):
            uploads = os.listdir(UPLOAD_FOLDER)

        if os.path.exists(PROCESSED_FOLDER):
            processed = os.listdir(PROCESSED_FOLDER)

        return jsonify({
            'uploads_folder': os.path.abspath(UPLOAD_FOLDER),
            'processed_folder': os.path.abspath(PROCESSED_FOLDER),
            'uploads': uploads,
            'processed': processed
        })
    except Exception as e:
        return jsonify({'error': str(e)})

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    return jsonify({'error': 'File too large. Maximum size is 16MB.'}), 413

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return render_template('index.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle server errors."""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
