import requests
import json
import cv2
import numpy as np
import os
from PIL import Image
import io

API_KEY = '89dPYAD2KeEl36Q_cJEuf395ZSm_8gjp'
API_SECRET = 'SbcMOTVHKhmeMD8bnNpl7ibJaGRJUBOS'
IMAGE_PATH = 'face2.jpeg'  # Local image path

DETECT_URL = 'https://api-us.faceplusplus.com/facepp/v3/detect'

# Supported image formats
SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.tif'}

def validate_image_format(image_path):
    """Validate if the image format is supported."""
    _, ext = os.path.splitext(image_path.lower())
    return ext in SUPPORTED_FORMATS

def convert_to_supported_format(image_path, output_format='JPEG'):
    """Convert image to a supported format if needed."""
    try:
        # Open image with PIL to handle various formats
        with Image.open(image_path) as img:
            # Convert RGBA to RGB if saving as JPEG
            if output_format == 'JPEG' and img.mode in ('RGBA', 'LA', 'P'):
                # Create white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background

            # Save to temporary file in memory
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format=output_format, quality=95)
            img_byte_arr.seek(0)
            return img_byte_arr
    except Exception as e:
        print(f"Error converting image format: {str(e)}")
        return None

def detect_face_and_landmarks(image_path):
    """Detect face and landmarks, supporting multiple image formats."""
    try:
        # Validate image format
        if not validate_image_format(image_path):
            print(f"Unsupported image format. Supported formats: {', '.join(SUPPORTED_FORMATS)}")
            return None, None

        # Convert image to supported format if needed
        converted_image = convert_to_supported_format(image_path)

        if converted_image:
            # Use converted image
            files = {'image_file': ('image.jpg', converted_image, 'image/jpeg')}
        else:
            # Use original image
            with open(image_path, 'rb') as f:
                files = {'image_file': f}

        data = {
            'api_key': API_KEY,
            'api_secret': API_SECRET,
            'return_landmark': 2  # Get all landmarks
        }

        if converted_image:
            response = requests.post(DETECT_URL, data=data, files=files)
        else:
            with open(image_path, 'rb') as f:
                files = {'image_file': f}
                response = requests.post(DETECT_URL, data=data, files=files)

        response.raise_for_status()
        resp_json = response.json()
        print("API response:", json.dumps(resp_json, indent=2))

        if 'faces' in resp_json and len(resp_json['faces']) > 0:
            face_token = resp_json['faces'][0]['face_token']
            landmarks = resp_json['faces'][0].get('landmark', {})
            return face_token, landmarks
        else:
            print("No face detected.")
            return None, None
    except Exception as e:
        print(f"Error in face detection: {str(e)}")
        return None, None

def slim_face(image_path, landmarks, output_path=None):
    """Apply face slimming effect, supporting multiple image formats."""
    try:
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError("Could not read the image file")

        h, w = img.shape[:2]

        # Check if required landmarks exist
        required_landmarks = ['contour_left5', 'contour_right5', 'contour_chin']
        for landmark in required_landmarks:
            if landmark not in landmarks:
                raise KeyError(f"Required landmark {landmark} not found")

        # Using three points from landmarks
        left = landmarks['contour_left5']
        right = landmarks['contour_right5']
        chin = landmarks['contour_chin']

        factor = 1.1

        new_left_x = int((left['x'] - chin['x']) / factor + chin['x'])
        new_right_x = int((right['x'] - chin['x']) / factor + chin['x'])

        src_points = np.float32([
            [left['x'], left['y']],
            [right['x'], right['y']],
            [chin['x'], chin['y']]
        ])

        dst_points = np.float32([
            [new_left_x, left['y']],
            [new_right_x, right['y']],
            [chin['x'], chin['y']]
        ])

        matrix = cv2.getAffineTransform(src_points, dst_points)
        slimmed = cv2.warpAffine(img, matrix, (w, h))

        # Save with appropriate format if output_path is provided
        if output_path:
            # Determine output format from extension
            _, ext = os.path.splitext(output_path.lower())
            if ext in {'.png'}:
                cv2.imwrite(output_path, slimmed, [cv2.IMWRITE_PNG_COMPRESSION, 9])
            elif ext in {'.jpg', '.jpeg'}:
                cv2.imwrite(output_path, slimmed, [cv2.IMWRITE_JPEG_QUALITY, 95])
            else:
                cv2.imwrite(output_path, slimmed)

        return slimmed
    except Exception as e:
        print(f"Error in face slimming: {str(e)}")
        return None

def process_image(input_path, output_path=None):
    """Process a single image with face slimming effect."""
    print(f"Processing image: {input_path}")

    # Validate input file exists
    if not os.path.exists(input_path):
        print(f"Error: Input file '{input_path}' does not exist.")
        return False

    print("Detecting face and landmarks...")
    face_token, landmarks = detect_face_and_landmarks(input_path)
    if not face_token or not landmarks:
        print("Face detection or landmark retrieval failed.")
        return False

    print("Applying slimming effect...")

    # Generate output path if not provided
    if not output_path:
        name, ext = os.path.splitext(input_path)
        output_path = f"{name}_slimmed{ext}"

    slimmed_img = slim_face(input_path, landmarks, output_path)
    if slimmed_img is None:
        print("Face slimming failed.")
        return False

    print(f"Slimmed image saved as {output_path}")
    return True

def main():
    """Main function for command line usage."""
    import sys

    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        input_path = IMAGE_PATH
        output_path = None

    success = process_image(input_path, output_path)
    if not success:
        print("Image processing failed.")
        sys.exit(1)

if __name__ == '__main__':
    main()