#!/usr/bin/env python3
"""
Simple test server to verify <PERSON><PERSON><PERSON> is working
"""

from flask import Flask, jsonify
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

@app.route('/')
def home():
    return jsonify({
        'status': 'success',
        'message': 'Face Slimmer AI Server is running!',
        'version': '1.0.0'
    })

@app.route('/health')
def health():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    print("Starting test server...")
    print("Server will be available at: http://127.0.0.1:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
