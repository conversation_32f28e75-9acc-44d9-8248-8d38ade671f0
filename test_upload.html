<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Slimmer AI - Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results {
            display: none;
            margin-top: 30px;
        }
        .image-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .image-container img {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Face Slimmer AI - Upload Test</h1>
        <p>This is a simple test page to verify the upload and processing functionality.</p>
        
        <div class="upload-area" id="uploadArea">
            <h3>📁 Drop your image here or click to browse</h3>
            <p>Supports: JPG, PNG, WEBP, BMP, TIFF (Max 16MB)</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
            <button class="btn" onclick="document.getElementById('fileInput').click()">
                Choose File
            </button>
        </div>
        
        <div class="status" id="status"></div>
        
        <div class="results" id="results">
            <h3>Results:</h3>
            <div class="image-container">
                <h4>Original</h4>
                <img id="originalImg" alt="Original image">
            </div>
            <div class="image-container">
                <h4>Enhanced</h4>
                <img id="processedImg" alt="Enhanced image">
            </div>
            <br>
            <button class="btn" id="downloadBtn">Download Enhanced Image</button>
            <button class="btn" onclick="resetTest()">Test Another Image</button>
        </div>
        
        <div class="log" id="log">
            <strong>Debug Log:</strong><br>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const status = document.getElementById('status');
        const results = document.getElementById('results');
        const originalImg = document.getElementById('originalImg');
        const processedImg = document.getElementById('processedImg');
        const downloadBtn = document.getElementById('downloadBtn');
        const log = document.getElementById('log');
        
        let currentDownloadUrl = null;
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            status.className = `status ${type}`;
            status.textContent = message;
            status.style.display = 'block';
            addLog(`STATUS (${type}): ${message}`);
        }
        
        // Event listeners
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                processFile(e.target.files[0]);
            }
        });
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                processFile(e.dataTransfer.files[0]);
            }
        });
        
        downloadBtn.addEventListener('click', () => {
            if (currentDownloadUrl) {
                window.open(currentDownloadUrl, '_blank');
            }
        });
        
        function processFile(file) {
            addLog(`=== PROCESSING FILE: ${file.name} ===`);
            addLog(`File size: ${file.size} bytes`);
            addLog(`File type: ${file.type}`);
            
            showStatus('Processing your image...', 'info');
            results.style.display = 'none';
            
            const formData = new FormData();
            formData.append('file', file);
            
            addLog('Sending upload request...');
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                addLog(`Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    showStatus('Image processed successfully!', 'success');
                    
                    // Load images
                    originalImg.src = data.original_url + '?t=' + Date.now();
                    processedImg.src = data.processed_url + '?t=' + Date.now();
                    currentDownloadUrl = data.download_url;
                    
                    results.style.display = 'block';
                    addLog('Images loaded successfully');
                } else {
                    showStatus(`Error: ${data.error}`, 'error');
                    addLog(`Processing failed: ${data.error}`);
                }
            })
            .catch(error => {
                addLog(`Upload error: ${error.message}`);
                showStatus(`Upload failed: ${error.message}`, 'error');
            });
        }
        
        function resetTest() {
            results.style.display = 'none';
            status.style.display = 'none';
            fileInput.value = '';
            currentDownloadUrl = null;
            addLog('=== TEST RESET ===');
        }
        
        // Initialize
        addLog('Face Slimmer AI Test Page Loaded');
        addLog('Server URL: ' + window.location.origin);
        
        // Test server connection
        fetch('/debug')
            .then(response => response.json())
            .then(data => {
                addLog('Server connection test: SUCCESS');
                addLog(`Server status: ${JSON.stringify(data, null, 2)}`);
                showStatus('Connected to server successfully', 'success');
            })
            .catch(error => {
                addLog('Server connection test: FAILED');
                addLog(`Connection error: ${error.message}`);
                showStatus('Warning: Could not connect to server', 'error');
            });
    </script>
</body>
</html>
