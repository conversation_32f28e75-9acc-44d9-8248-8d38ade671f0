#!/usr/bin/env python3
"""
Simplified Flask app for Face Slimmer AI
"""

from flask import Flask, render_template, request, jsonify, send_file, url_for
import os
import uuid
import logging
import time
from werkzeug.utils import secure_filename
from face_slimmer import process_image, SUPPORTED_FORMATS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'face-slimmer-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create necessary directories
UPLOAD_FOLDER = 'uploads'
PROCESSED_FOLDER = 'processed'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    if '.' not in filename:
        return False
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    return ext in SUPPORTED_FORMATS

@app.route('/')
def index():
    """Main page with upload form."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and processing."""
    input_path = None
    output_path = None

    try:
        logger.info("=== UPLOAD REQUEST RECEIVED ===")

        # Check if file is in request
        if 'file' not in request.files:
            logger.warning("No file in request")
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        file = request.files['file']
        if file.filename == '':
            logger.warning("Empty filename")
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Get file size from the actual file content
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        logger.info(f"Processing file: {file.filename}, size: {file_size} bytes")

        # Validate file format
        if not allowed_file(file.filename):
            supported = ', '.join(sorted(SUPPORTED_FORMATS))
            logger.warning(f"Unsupported file format: {file.filename}")
            return jsonify({'success': False, 'error': f'Unsupported file format. Supported formats: {supported}'}), 400

        # Validate file size
        if file_size > 16 * 1024 * 1024:  # 16MB
            logger.warning(f"File too large: {file_size} bytes")
            return jsonify({'success': False, 'error': 'File too large. Maximum size is 16MB.'}), 400

        # Generate unique filename with timestamp for better uniqueness
        import time
        timestamp = str(int(time.time()))
        unique_id = str(uuid.uuid4())[:8]
        original_ext = '.' + file.filename.rsplit('.', 1)[1].lower()

        input_filename = f"{timestamp}_{unique_id}_input{original_ext}"
        output_filename = f"{timestamp}_{unique_id}_output.jpg"  # Always output as JPG for consistency

        input_path = os.path.join(UPLOAD_FOLDER, input_filename)
        output_path = os.path.join(PROCESSED_FOLDER, output_filename)

        # Ensure directories exist
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(PROCESSED_FOLDER, exist_ok=True)

        # Save uploaded file
        logger.info(f"Saving file to: {os.path.abspath(input_path)}")
        file.save(input_path)

        # Verify file was saved and has content
        if not os.path.exists(input_path):
            logger.error(f"Failed to save input file: {input_path}")
            return jsonify({'success': False, 'error': 'Failed to save uploaded file'}), 500

        saved_size = os.path.getsize(input_path)
        if saved_size == 0:
            logger.error(f"Saved file is empty: {input_path}")
            os.remove(input_path)
            return jsonify({'success': False, 'error': 'Uploaded file is empty'}), 500

        logger.info(f"Input file saved successfully, size: {saved_size} bytes")

        # Process the image
        logger.info("Starting image processing...")
        try:
            success = process_image(input_path, output_path)
        except Exception as process_error:
            logger.error(f"Processing error: {str(process_error)}")
            success = False

        if not success:
            logger.error("Image processing failed")
            # Clean up input file
            if os.path.exists(input_path):
                os.remove(input_path)
            return jsonify({'success': False, 'error': 'Failed to process image. Please ensure the image contains a clear, visible face.'}), 400

        # Verify output file was created
        if not os.path.exists(output_path):
            logger.error(f"Output file was not created: {output_path}")
            # Clean up input file
            if os.path.exists(input_path):
                os.remove(input_path)
            return jsonify({'success': False, 'error': 'Processing completed but output file was not created'}), 500

        output_size = os.path.getsize(output_path)
        if output_size == 0:
            logger.error(f"Output file is empty: {output_path}")
            # Clean up files
            if os.path.exists(input_path):
                os.remove(input_path)
            if os.path.exists(output_path):
                os.remove(output_path)
            return jsonify({'success': False, 'error': 'Processing created empty output file'}), 500

        logger.info(f"Processing completed successfully! Output file size: {output_size} bytes")

        # Return success response with file URLs
        response_data = {
            'success': True,
            'original_url': url_for('serve_file', folder='uploads', filename=input_filename),
            'processed_url': url_for('serve_file', folder='processed', filename=output_filename),
            'download_url': url_for('download_file', filename=output_filename),
            'original_filename': file.filename,
            'processed_filename': output_filename
        }

        logger.info(f"Returning success response with URLs: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Unexpected error in upload_file: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # Clean up files if they exist
        if input_path and os.path.exists(input_path):
            try:
                os.remove(input_path)
                logger.info(f"Cleaned up input file: {input_path}")
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup input file: {cleanup_error}")

        if output_path and os.path.exists(output_path):
            try:
                os.remove(output_path)
                logger.info(f"Cleaned up output file: {output_path}")
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup output file: {cleanup_error}")

        return jsonify({'success': False, 'error': f'An unexpected error occurred: {str(e)}'}), 500

@app.route('/files/<folder>/<filename>')
def serve_file(folder, filename):
    """Serve uploaded or processed files."""
    try:
        logger.info(f"=== SERVING FILE: {folder}/{filename} ===")

        # Validate folder
        if folder not in ['uploads', 'processed']:
            logger.error(f"Invalid folder requested: {folder}")
            return "Invalid folder", 404

        # Secure the filename
        filename = secure_filename(filename)
        file_path = os.path.join(folder, filename)
        abs_path = os.path.abspath(file_path)

        logger.info(f"Looking for file at: {abs_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {abs_path}")
            # List available files for debugging
            if os.path.exists(folder):
                try:
                    available_files = os.listdir(folder)
                    logger.info(f"Available files in {folder}: {available_files}")
                except Exception as list_error:
                    logger.error(f"Could not list files in {folder}: {list_error}")
            return "File not found", 404

        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"File is empty: {abs_path}")
            return "File is empty", 404

        logger.info(f"Successfully serving file: {abs_path} (size: {file_size} bytes)")

        # Determine MIME type based on file extension
        file_ext = filename.lower().split('.')[-1]
        mime_types = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'webp': 'image/webp',
            'bmp': 'image/bmp',
            'tiff': 'image/tiff',
            'tif': 'image/tiff'
        }

        mime_type = mime_types.get(file_ext, 'image/jpeg')

        # Add CORS headers and cache control
        response = send_file(
            file_path,
            mimetype=mime_type,
            as_attachment=False,
            download_name=filename
        )

        # Add headers for better browser compatibility
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        logger.error(f"Error serving file {folder}/{filename}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return f"Error serving file: {str(e)}", 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download processed file."""
    try:
        file_path = os.path.join(PROCESSED_FOLDER, filename)
        logger.info(f"Download requested for: {os.path.abspath(file_path)}")
        
        if not os.path.exists(file_path):
            logger.error(f"Download file not found: {file_path}")
            return "File not found", 404
        
        return send_file(file_path, as_attachment=True, download_name=f"slimmed_{filename}")
        
    except Exception as e:
        logger.error(f"Error downloading file {filename}: {str(e)}")
        return f"Error downloading file: {str(e)}", 500

@app.route('/debug')
def debug_info():
    """Debug route to check server status."""
    try:
        uploads = []
        processed = []

        # Get file lists
        if os.path.exists(UPLOAD_FOLDER):
            uploads = os.listdir(UPLOAD_FOLDER)

        if os.path.exists(PROCESSED_FOLDER):
            processed = os.listdir(PROCESSED_FOLDER)

        # Get system info
        import platform
        import sys

        return jsonify({
            'status': 'Flask server is running!',
            'timestamp': str(int(time.time())),
            'uploads_folder': os.path.abspath(UPLOAD_FOLDER),
            'processed_folder': os.path.abspath(PROCESSED_FOLDER),
            'uploads': uploads,
            'processed': processed,
            'supported_formats': list(SUPPORTED_FORMATS),
            'system_info': {
                'platform': platform.system(),
                'python_version': sys.version,
                'working_directory': os.getcwd()
            }
        })
    except Exception as e:
        logger.error(f"Debug route error: {str(e)}")
        return jsonify({'error': str(e), 'status': 'error'})

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    return jsonify({'error': 'File too large. Maximum size is 16MB.'}), 413

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return render_template('index.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle server errors."""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 Starting Face Slimmer AI Web Application")
    print("=" * 60)
    print(f"📁 Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"📁 Processed folder: {os.path.abspath(PROCESSED_FOLDER)}")
    print(f"🎯 Supported formats: {', '.join(SUPPORTED_FORMATS)}")
    print("=" * 60)
    print("🌐 Server will be available at: http://127.0.0.1:5000")
    print("🔧 Debug info available at: http://127.0.0.1:5000/debug")
    print("=" * 60)
    
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
