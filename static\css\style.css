/* Reset and Base Styles */
:root {
    --primary: #667eea;
    --primary-dark: #5a67d8;
    --secondary: #764ba2;
    --success: #48bb78;
    --error: #e53e3e;
    --warning: #ed8936;
    --dark: #2d3748;
    --gray-dark: #4a5568;
    --gray: #718096;
    --gray-light: #a0aec0;
    --light: #e2e8f0;
    --lighter: #f7fafc;
    --white: #ffffff;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --rounded-sm: 0.125rem;
    --rounded: 0.25rem;
    --rounded-md: 0.375rem;
    --rounded-lg: 0.5rem;
    --rounded-xl: 0.75rem;
    --rounded-2xl: 1rem;
    --rounded-3xl: 1.5rem;
    --rounded-full: 9999px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--lighter);
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--rounded-lg);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary) 100%);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
}

/* Header & Navigation */
.header {
    padding: 20px 0;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark);
}

.logo i {
    color: var(--primary);
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
}

.nav-links a {
    text-decoration: none;
    color: var(--gray-dark);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary);
}

.auth-buttons {
    display: flex;
    gap: 15px;
}

/* Hero Section */
.hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    padding: 60px 0;
}

.hero-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.hero h1 {
    font-size: 3rem;
    line-height: 1.2;
}

.hero h1 span {
    color: var(--primary);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--gray);
    max-width: 500px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.trust-badges {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.badge {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--gray-dark);
}

.badge i {
    color: var(--primary);
}

.hero-image {
    position: relative;
}

.image-comparison {
    position: relative;
    width: 100%;
    border-radius: var(--rounded-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.before, .after {
    position: relative;
    width: 100%;
    height: 100%;
}

.before img, .after img {
    width: 100%;
    height: auto;
    display: block;
}

/* Removed old pseudo-element labels - now using proper HTML elements */

/* Upload Section */
.upload-section {
    padding: 60px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.section-header p {
    color: var(--gray);
    font-size: 1.1rem;
}

.upload-card {
    background: var(--white);
    border-radius: var(--rounded-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1000px;
    margin: 0 auto;
}

.upload-area {
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-right: 1px solid var(--light);
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
    position: relative;
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 20px;
}

.pulse-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.8); opacity: 0.7; }
    70% { transform: scale(1.3); opacity: 0; }
    100% { transform: scale(0.8); opacity: 0; }
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.file-types {
    display: flex;
    gap: 10px;
    margin: 20px 0;
}

.file-types span {
    background: var(--light);
    padding: 5px 10px;
    border-radius: var(--rounded);
    font-size: 0.8rem;
    color: var(--gray-dark);
}

.upload-tips {
    padding: 40px;
    background: var(--lighter);
}

.upload-tips h4 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.upload-tips ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.upload-tips li {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--gray-dark);
}

.upload-tips i {
    color: var(--primary);
}

/* Processing Section */
.processing-section {
    padding: 80px 0;
    background: var(--lighter);
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.processing-section.show {
    display: block;
    opacity: 1;
}

.processing-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.processing-animation {
    margin-bottom: 40px;
}

.ai-robot {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}

.robot-head {
    width: 100px;
    height: 100px;
    background: var(--white);
    border-radius: var(--rounded-2xl);
    margin: 0 auto;
    position: relative;
    box-shadow: var(--shadow-md);
}

.robot-eyes {
    display: flex;
    justify-content: center;
    gap: 20px;
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
}

.eye {
    width: 20px;
    height: 20px;
    background: var(--primary);
    border-radius: 50%;
    animation: blink 4s infinite;
}

@keyframes blink {
    0%, 45%, 55%, 100% { height: 20px; }
    50% { height: 5px; }
}

.progress-waves {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.wave {
    width: 10px;
    height: 20px;
    background: var(--primary);
    border-radius: var(--rounded-full);
    animation: wave 1.5s infinite ease-in-out;
}

.wave:nth-child(2) {
    animation-delay: 0.2s;
}

.wave:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes wave {
    0%, 100% { height: 20px; }
    50% { height: 50px; }
}

.progress-container {
    margin-top: 40px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: var(--gray-dark);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light);
    border-radius: var(--rounded-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    width: 0%;
    transition: width 0.3s ease;
}

.fun-facts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 40px;
}

.fact {
    background: var(--white);
    padding: 20px;
    border-radius: var(--rounded-lg);
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: var(--shadow-sm);
}

.fact i {
    font-size: 1.5rem;
    color: var(--primary);
}

/* Results Section */
.results-section {
    padding: 80px 0;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.results-section.show {
    display: block;
    opacity: 1;
}

.results-container {
    max-width: 1200px;
    margin: 0 auto;
}

.image-comparison-container {
    margin-bottom: 40px;
}

.comparison-slider {
    position: relative;
    width: 100%;
    aspect-ratio: 1 / 1;
    max-width: 600px;
    margin: 0 auto;
    border-radius: var(--rounded-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.comparison-slider .before,
.comparison-slider .after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: none !important;
    opacity: 1 !important;
}

.comparison-slider .after {
    clip-path: inset(0 50% 0 0);
    transition: clip-path 0.1s ease-out;
}

.comparison-slider .before img,
.comparison-slider .after img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 1 !important;
    filter: none !important;
    background: none !important;
    display: block;
}

/* Ensure no pseudo-elements interfere with comparison slider images */
.comparison-slider .before::before,
.comparison-slider .before::after,
.comparison-slider .after::before,
.comparison-slider .after::after {
    display: none !important;
}

.comparison-slider .before .label {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: var(--rounded);
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 5;
    pointer-events: none;
}

.comparison-slider .after .label {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: var(--rounded);
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 5;
    pointer-events: none;
}

.slider-handle {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: var(--white);
    transform: translateX(-50%);
    z-index: 10;
    cursor: grab;
    transition: left 0.1s ease-out;
}

.slider-handle:active {
    cursor: grabbing;
}

.slider-line {
    width: 100%;
    height: 100%;
    background: var(--white);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.slider-button {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(-50%, -50%);
    box-shadow: var(--shadow-lg);
    color: var(--primary);
    font-size: 1.2rem;
    transition: transform 0.2s ease;
}

.slider-button:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

.enhancement-details {
    margin-bottom: 40px;
}

.enhancement-details h4 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.enhancement-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.enhancement-item {
    display: flex;
    gap: 15px;
    background: var(--lighter);
    padding: 20px;
    border-radius: var(--rounded-lg);
}

.enhancement-item .icon {
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: var(--rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary);
    flex-shrink: 0;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.secondary-actions {
    display: flex;
    gap: 15px;
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: var(--lighter);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: var(--rounded-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius: var(--rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 1.8rem;
}

.feature-card h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* How It Works Section */
.how-it-works-section {
    padding: 80px 0;
}

.steps-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
    position: relative;
}

.steps-container::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50px;
    width: 4px;
    background: var(--light);
    transform: translateX(-50%);
    z-index: 1;
}

.step {
    display: flex;
    gap: 30px;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 100px;
    height: 100px;
    background: var(--white);
    border: 4px solid var(--primary);
    border-radius: var(--rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
}

.step-content {
    padding: 20px 0;
}

.step-content h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: var(--lighter);
}

.testimonials-slider {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: var(--white);
    padding: 30px;
    border-radius: var(--rounded-xl);
    box-shadow: var(--shadow-sm);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.user-info img {
    width: 60px;
    height: 60px;
    border-radius: var(--rounded-full);
    object-fit: cover;
}

.stars {
    color: var(--warning);
    margin-top: 5px;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 15px;
    border-radius: var(--rounded-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.faq-question {
    width: 100%;
    padding: 20px;
    background: var(--white);
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: left;
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-question.active i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--lighter);
}

.faq-answer p {
    padding: 0 20px 20px;
}

/* Footer */
.footer {
    background: var(--dark);
    color: var(--white);
    padding: 60px 0 0;
    margin-top: 80px;
}

.footer-container {
    width: 100%;
    margin: 0;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    padding: 0 60px;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.footer-brand .logo {
    color: var(--white);
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-brand .logo i {
    color: var(--primary);
    margin-right: 10px;
}

.footer-brand p {
    opacity: 0.8;
    line-height: 1.6;
    max-width: 300px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    color: var(--white);
    opacity: 0.7;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    opacity: 1;
    background: var(--primary);
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 80px;
}

.link-column h4 {
    margin-bottom: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--white);
}

.link-column a {
    display: block;
    color: var(--white);
    opacity: 0.8;
    margin-bottom: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.link-column a:hover {
    opacity: 1;
    color: var(--primary);
    padding-left: 5px;
}

.footer-bottom {
    padding: 30px 60px;
    margin-top: 50px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom p {
    opacity: 0.7;
    font-size: 0.9rem;
}

.footer-links-bottom {
    display: flex;
    gap: 20px;
}

.footer-links-bottom a {
    color: var(--white);
    opacity: 0.7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: opacity 0.3s ease;
}

.footer-links-bottom a:hover {
    opacity: 1;
}

/* Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 350px;
    background: var(--white);
    border-radius: var(--rounded-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 100;
}

.chat-widget.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.chat-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-close {
    background: transparent;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
}

.chat-body {
    height: 300px;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: var(--rounded-lg);
    font-size: 0.9rem;
}

.chat-message.bot {
    align-self: flex-start;
    background: var(--lighter);
}

.chat-message.user {
    align-self: flex-end;
    background: var(--primary);
    color: var(--white);
}

.chat-input {
    display: flex;
    padding: 15px;
    border-top: 1px solid var(--light);
}

.chat-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--light);
    border-radius: var(--rounded-lg);
    outline: none;
}

.chat-input button {
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--rounded-lg);
    padding: 0 15px;
    margin-left: 10px;
    cursor: pointer;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: var(--white);
    padding: 15px 20px;
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 15px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
}

.toast i {
    font-size: 1.5rem;
}

.toast.success {
    border-left: 4px solid var(--success);
}

.toast.success i {
    color: var(--success);
}

.toast.error {
    border-left: 4px solid var(--error);
}

.toast.error i {
    color: var(--error);
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast.warning i {
    color: var(--warning);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        grid-template-columns: 1fr;
        gap: 40px;
        padding: 40px 0;
    }
    
    .hero-image {
        order: -1;
        max-width: 600px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 20px;
    }
    
    .nav-links {
        gap: 15px;
    }
    
    .auth-buttons {
        gap: 10px;
    }
    
    .hero h1 {
        font-size: 2.2rem;
    }
    
    .upload-card {
        grid-template-columns: 1fr;
    }
    
    .upload-area {
        border-right: none;
        border-bottom: 1px solid var(--light);
    }
    
    .steps-container::before {
        display: none;
    }
    
    .step {
        flex-direction: column;
        gap: 15px;
    }
    
    .step-number {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: 50px;
        padding: 0 40px;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 50px;
    }

    .footer-bottom {
        padding: 30px 40px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .trust-badges {
        flex-direction: column;
        gap: 10px;
    }
    
    .comparison-slider {
        aspect-ratio: 1 / 1;
        max-width: 90vw;
    }
    
    .secondary-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer {
        margin-top: 60px;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: 35px;
        padding: 0 25px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: 15px;
        padding: 30px 25px;
    }

    .footer-links-bottom {
        gap: 15px;
    }

    .chat-widget {
        width: calc(100% - 40px);
        right: 20px;
    }
}