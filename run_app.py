#!/usr/bin/env python3
"""
Simple script to run the Face Slimmer AI web application
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Starting Face Slimmer AI Web Application...")
    print("Importing modules...")
    
    from app import app
    
    print("Flask app imported successfully!")
    print("Starting server on http://127.0.0.1:5000")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    # Run the Flask app
    app.run(
        debug=True,
        host='127.0.0.1',
        port=5000,
        use_reloader=False  # Disable reloader to avoid issues
    )
    
except ImportError as e:
    print(f"Import Error: {e}")
    print("Please make sure all dependencies are installed:")
    print("pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as e:
    print(f"Error starting application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
