#!/usr/bin/env python3
"""
Comprehensive startup script for Face Slimmer AI
This script will diagnose issues and start the server properly
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def print_header(title):
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def print_status(message, status="INFO"):
    icons = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARNING": "⚠️"}
    print(f"{icons.get(status, 'ℹ️')} {message}")

def check_dependencies():
    print_header("CHECKING DEPENDENCIES")
    
    required_packages = [
        'flask', 'opencv-python', 'numpy', 'requests', 'Pillow', 'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print_status(f"{package} - OK", "SUCCESS")
        except ImportError:
            print_status(f"{package} - MISSING", "ERROR")
            missing_packages.append(package)
    
    if missing_packages:
        print_status(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
        print_status("Installing missing packages...", "INFO")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print_status("Packages installed successfully", "SUCCESS")
        except subprocess.CalledProcessError as e:
            print_status(f"Failed to install packages: {e}", "ERROR")
            return False
    
    return True

def check_files():
    print_header("CHECKING FILES")
    
    required_files = [
        'simple_app.py',
        'face_slimmer.py',
        'templates/index.html',
        'static/css/style.css',
        'static/js/script.js'
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print_status(f"{file_path} - OK ({size} bytes)", "SUCCESS")
        else:
            print_status(f"{file_path} - MISSING", "ERROR")
            all_files_exist = False
    
    return all_files_exist

def check_directories():
    print_header("CHECKING DIRECTORIES")
    
    required_dirs = ['uploads', 'processed', 'static', 'templates', 'static/css', 'static/js']
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            print_status(f"Creating directory: {dir_path}", "INFO")
            os.makedirs(dir_path, exist_ok=True)
        
        if os.path.exists(dir_path):
            print_status(f"{dir_path} - OK", "SUCCESS")
        else:
            print_status(f"{dir_path} - FAILED TO CREATE", "ERROR")

def test_face_processing():
    print_header("TESTING FACE PROCESSING")
    
    try:
        from face_slimmer import SUPPORTED_FORMATS, process_image
        print_status(f"Face slimmer module imported successfully", "SUCCESS")
        print_status(f"Supported formats: {', '.join(SUPPORTED_FORMATS)}", "INFO")
        
        # Check if test image exists
        test_images = ['face2.jpeg', 'face.jpg', 'test.jpg']
        test_image = None
        
        for img in test_images:
            if os.path.exists(img):
                test_image = img
                break
        
        if test_image:
            print_status(f"Found test image: {test_image}", "SUCCESS")
            # We won't actually process it here to save API calls
        else:
            print_status("No test image found (face2.jpeg, face.jpg, or test.jpg)", "WARNING")
        
        return True
        
    except Exception as e:
        print_status(f"Face processing test failed: {str(e)}", "ERROR")
        return False

def start_server():
    print_header("STARTING SERVER")
    
    try:
        print_status("Starting Flask server...", "INFO")
        print_status("Server will be available at: http://127.0.0.1:5000", "INFO")
        print_status("Press Ctrl+C to stop the server", "INFO")
        print_status("Opening browser in 3 seconds...", "INFO")
        
        # Import and run the Flask app
        from simple_app import app
        
        # Start server in a separate thread so we can open browser
        import threading
        import webbrowser
        
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://127.0.0.1:5000')
                print_status("Browser opened", "SUCCESS")
            except Exception as e:
                print_status(f"Could not open browser: {e}", "WARNING")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        
    except Exception as e:
        print_status(f"Failed to start server: {str(e)}", "ERROR")
        return False

def main():
    print_header("FACE SLIMMER AI - STARTUP DIAGNOSTICS")
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print_status(f"Working directory: {script_dir}", "INFO")
    
    # Run all checks
    checks = [
        ("Dependencies", check_dependencies),
        ("Files", check_files),
        ("Directories", check_directories),
        ("Face Processing", test_face_processing)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
                print_status(f"{check_name} check failed", "ERROR")
        except Exception as e:
            print_status(f"{check_name} check error: {str(e)}", "ERROR")
            all_passed = False
    
    if all_passed:
        print_status("All checks passed! Starting server...", "SUCCESS")
        start_server()
    else:
        print_status("Some checks failed. Please fix the issues above.", "ERROR")
        print_status("You can still try to start the server manually with: python simple_app.py", "INFO")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_status("\nServer stopped by user", "INFO")
        sys.exit(0)
    except Exception as e:
        print_status(f"Unexpected error: {str(e)}", "ERROR")
        sys.exit(1)
