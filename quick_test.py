#!/usr/bin/env python3
"""
Quick test to verify everything works
"""

import os
from face_slimmer import process_image

def quick_test():
    print("🧪 Quick Face Slimmer Test")
    print("=" * 40)
    
    # Test with your image
    input_image = "face2.jpeg"
    output_image = "quick_test_output.jpg"
    
    if not os.path.exists(input_image):
        print(f"❌ Test image {input_image} not found!")
        return False
    
    print(f"📸 Processing: {input_image}")
    success = process_image(input_image, output_image)
    
    if success and os.path.exists(output_image):
        print(f"✅ Success! Output saved to: {output_image}")
        print(f"📊 File size: {os.path.getsize(output_image)} bytes")
        
        # Open the result
        try:
            os.startfile(output_image)  # Windows
            print("🖼️  Opening result image...")
        except:
            print(f"📁 Result saved at: {os.path.abspath(output_image)}")
        
        return True
    else:
        print("❌ Processing failed!")
        return False

if __name__ == "__main__":
    quick_test()
